import os
import sys
from pathlib import Path

# Add project root to sys.path to allow running alembic from the 'backend' directory
# This makes both local execution and docker execution work
if 'APP_ENV' in os.environ and os.environ['APP_ENV'] == 'docker':
    # In Docker, the project root is /app
    sys.path.insert(0, '/app')
else:
    # For local execution, calculate the path
    # (backend/alembic/env.py -> backend/alembic -> backend -> project_root)
    project_root = Path(__file__).resolve().parents[2]
    sys.path.insert(0, str(project_root))

from logging.config import fileConfig

from sqlalchemy import create_engine

from alembic import context

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
from backend.db.base import Base
from backend.core.config import settings

target_metadata = Base.metadata

def get_db_url():
    url = settings.DATABASE_URL
    if url and url.startswith("postgresql+asyncpg://"):
        url = url.replace("+asyncpg", "+psycopg2")
    return url

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'online' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = get_db_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    url = get_db_url()
    connectable = create_engine(url)

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
