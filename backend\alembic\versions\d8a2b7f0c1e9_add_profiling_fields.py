"""add profiling fields to data source

Revision ID: d8a2b7f0c1e9
Revises: 
Create Date: 2025-07-03 21:25:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd8a2b7f0c1e9'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('data_source', sa.Column('profiling_status', sa.String(), nullable=True))
    op.add_column('data_source', sa.Column('profiling_result', sa.JSON(), nullable=True))
    op.add_column('data_source', sa.Column('task_id', sa.String(), nullable=True))
    op.create_index(op.f('ix_data_source_profiling_status'), 'data_source', ['profiling_status'], unique=False)
    op.create_index(op.f('ix_data_source_task_id'), 'data_source', ['task_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_data_source_task_id'), table_name='data_source')
    op.drop_index(op.f('ix_data_source_profiling_status'), table_name='data_source')
    op.drop_column('data_source', 'task_id')
    op.drop_column('data_source', 'profiling_result')
    op.drop_column('data_source', 'profiling_status')
    # ### end Alembic commands ### 