# 应用配置
APP_NAME=多模态智能数据分析平台
APP_VERSION=1.0.0
APP_ENV=development
DEBUG=True

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1

# 安全配置 - 生产环境请修改
SECRET_KEY=your-secret-key-for-dev-change-in-production-2024
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# PostgreSQL - 使用现有的TimescaleDB容器
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5433/multimodal_analysis
SYNC_DATABASE_URL=postgresql://postgres:password@localhost:5433/multimodal_analysis
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30

# MongoDB - 使用现有容器
MONGODB_URL=mongodb://localhost:27018
MONGODB_DATABASE=multimodal_analysis

# Redis - 项目专用容器
REDIS_URL=redis://localhost:6380/0
REDIS_PASSWORD=multimodal123

# Milvus - 向量数据库
MILVUS_HOST=localhost
MILVUS_PORT=19530

# Neo4j - 图数据库（如果启动了）
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password123

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://localhost:5173"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# AI模型配置
MODEL_CACHE_DIR=./models
EMBEDDING_MODEL=BAAI/bge-base-zh-v1.5
LLM_MODEL=chatglm3-6b

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=104857600
ALLOWED_EXTENSIONS=["pdf", "docx", "txt", "csv", "xlsx", "json", "jpg", "png", "mp3", "mp4"]

# Celery配置（异步任务）
CELERY_BROKER_URL=redis://:multimodal123@localhost:6380/1
CELERY_RESULT_BACKEND=redis://:multimodal123@localhost:6380/2 