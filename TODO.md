# All Data Solution Sys - TODO List

为下一阶段的开发工作制定清晰的路线图，确保项目高效、有序地推进。

## 已完成的任务 ✅

- [x] **前端UI现代化改造**:
  - [x] 重新设计登录页面和仪表板页面
  - [x] 实现现代化设计系统（CSS变量、渐变背景、玻璃态效果）
  - [x] 改进卡片、按钮、输入框等基础组件样式
  - [x] 添加动画效果和响应式布局
  - [x] 修复Modal组件的isOpen属性问题

- [x] **基础设施配置**:
  - [x] 确认API端口配置（后端8008，前端3000）
  - [x] 验证数据库服务可用性（PostgreSQL、Redis、MongoDB、Neo4j）
  - [x] Docker容器配置优化

## 优先级最高的任务 (Next Sprint) 🚀

### P0: 前端集成与用户体验闭环
- [ ] **修复项目列表获取失败问题**:
  - [ ] 排查认证Token处理逻辑
  - [ ] 完善错误处理和用户提示
  - [ ] 添加请求重试机制

- [ ] **对接异步数据分析API**:
  - [ ] 修改前端页面，将"数据分析"按钮的点击事件改为调用新的 `POST /api/v1/processing/profile/{data_source_id}` 接口
  - [ ] 在状态管理中保存接口返回的 `task_id`

- [ ] **实现任务状态轮询与展示**:
  - [ ] 发起分析后显示"分析中..."的加载状态
  - [ ] 使用 `task_id` 定期轮询 `GET /api/v1/processing/profile/{task_id}` 接口
  - [ ] 根据 `profiling_status` 更新UI状态

- [ ] **分析结果可视化**:
  - [ ] 获取并解析 `profiling_result` 中的JSON数据
  - [ ] 使用 `echarts` 或 `plotly` 实现数据可视化
  - [ ] 优化报告展示布局

### P1: 后端能力扩展与健壮性提升 💪

- [ ] **扩展核心分析能力**:
  - [ ] **文本分析任务**: 为 `.txt`, `.csv` 文件增加主题建模、关键词提取、情感分析等功能
  - [ ] **图像分析任务**: 为 `.jpg`, `.png` 文件实现图像指纹和相似图片查找

- [ ] **提升系统健壮性**:
  - [ ] 为Celery任务添加自动重试机制
  - [ ] 完善错误日志记录
  - [ ] 添加系统监控指标

- [ ] **编写自动化测试**:
  - [ ] 编写API集成测试
  - [ ] 编写Celery任务单元测试
  - [ ] 添加前端组件测试

### P2: 探索核心AI功能：语义检索 (RAG) 🤖

- [ ] **设计并实现Embedding流程**:
  - [ ] 实现文件内容分块（Chunking）
  - [ ] 集成DeepSeek模型生成向量嵌入

- [ ] **实现向量索引与存储**:
  - [ ] 配置Milvus向量数据库
  - [ ] 设计向量存储结构
  - [ ] 实现向量索引和检索接口

- [ ] **构建基础检索API**:
  - [ ] 创建自然语言查询接口
  - [ ] 实现向量相似度搜索
  - [ ] 优化检索结果排序

## 未来规划 🔮

- [ ] **性能优化**:
  - [ ] 前端代码分割和懒加载
  - [ ] API响应缓存优化
  - [ ] 数据库查询优化

- [ ] **用户体验提升**:
  - [ ] 添加操作引导和帮助文档
  - [ ] 实现批量操作功能
  - [ ] 优化移动端适配

- [ ] **DevOps完善**:
  - [ ] 配置CI/CD流程
  - [ ] 完善监控告警
  - [ ] 优化部署流程 